import { NextRequest, NextResponse } from 'next/server';
import { ApiResponse } from '@/types';

interface ImageResult {
  id: string;
  url: string;
  thumbnailUrl: string;
  alt: string;
  photographer: string;
  source: 'unsplash' | 'pexels';
  downloadUrl: string;
}

// GET /api/images/search - Search for images (placeholder for now)
export async function GET(request: NextRequest): Promise<NextResponse<ApiResponse<ImageResult[]>>> {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q');
    const page = parseInt(searchParams.get('page') || '1');
    const perPage = parseInt(searchParams.get('per_page') || '20');

    if (!query) {
      return NextResponse.json({
        success: false,
        error: 'Search query is required'
      }, { status: 400 });
    }

    // Placeholder response - will be replaced with actual API integration in Phase 4
    const mockImages: ImageResult[] = [
      {
        id: 'mock-1',
        url: 'https://images.unsplash.com/photo-1557804506-669a67965ba0?w=800',
        thumbnailUrl: 'https://images.unsplash.com/photo-1557804506-669a67965ba0?w=400',
        alt: `${query} related image`,
        photographer: 'Mock Photographer',
        source: 'unsplash',
        downloadUrl: 'https://images.unsplash.com/photo-1557804506-669a67965ba0?w=1200'
      },
      {
        id: 'mock-2',
        url: 'https://images.unsplash.com/photo-1542744173-8e7e53415bb0?w=800',
        thumbnailUrl: 'https://images.unsplash.com/photo-1542744173-8e7e53415bb0?w=400',
        alt: `${query} business image`,
        photographer: 'Another Photographer',
        source: 'unsplash',
        downloadUrl: 'https://images.unsplash.com/photo-1542744173-8e7e53415bb0?w=1200'
      }
    ];

    return NextResponse.json({
      success: true,
      data: mockImages,
      message: `Found ${mockImages.length} images for "${query}"`
    });
  } catch (error) {
    console.error('Error searching images:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to search images'
    }, { status: 500 });
  }
}
