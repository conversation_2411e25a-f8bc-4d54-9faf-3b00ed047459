import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Presentation from '@/models/Presentation';
import { aiService } from '@/lib/ai-service';
import { ApiResponse, GenerationOptions, Presentation as PresentationType } from '@/types';

// POST /api/presentations/generate - Generate a new presentation with AI
export async function POST(request: NextRequest): Promise<NextResponse<ApiResponse<PresentationType>>> {
  try {
    await connectDB();
    
    const body = await request.json();
    const { 
      userId, 
      topic, 
      audience = 'general', 
      slideCount = 8, 
      tone = 'professional', 
      template = 'modern' 
    } = body;

    // Validate required fields
    if (!userId || !topic) {
      return NextResponse.json({
        success: false,
        error: 'User ID and topic are required'
      }, { status: 400 });
    }

    // Validate slide count
    if (slideCount < 3 || slideCount > 50) {
      return NextResponse.json({
        success: false,
        error: 'Slide count must be between 3 and 50'
      }, { status: 400 });
    }

    const generationOptions: GenerationOptions = {
      topic,
      audience,
      slideCount,
      tone,
      template
    };

    // Generate presentation with AI
    const generatedPresentation = await aiService.generateFullPresentation(generationOptions);

    // Calculate word count
    const wordCount = generatedPresentation.slides.reduce((total, slide) => {
      return total + slide.title.split(' ').length + slide.content.split(' ').length;
    }, 0);

    // Create presentation in database
    const presentation = new Presentation({
      userId,
      title: generatedPresentation.title,
      topic,
      slides: generatedPresentation.slides,
      template,
      settings: {
        isPublic: false,
        allowComments: false
      },
      metadata: {
        createdAt: new Date(),
        updatedAt: new Date(),
        generationTime: generatedPresentation.generationTime,
        wordCount
      }
    });

    const savedPresentation = await presentation.save();

    return NextResponse.json({
      success: true,
      data: {
        ...savedPresentation.toObject(),
        _id: savedPresentation._id.toString()
      },
      message: `Presentation "${generatedPresentation.title}" generated successfully in ${generatedPresentation.generationTime}ms`
    }, { status: 201 });

  } catch (error) {
    console.error('Error generating presentation:', error);
    
    // Handle specific AI service errors
    if (error instanceof Error && error.message.includes('Failed to generate')) {
      return NextResponse.json({
        success: false,
        error: 'AI service temporarily unavailable. Please try again.'
      }, { status: 503 });
    }

    return NextResponse.json({
      success: false,
      error: 'Failed to generate presentation'
    }, { status: 500 });
  }
}
