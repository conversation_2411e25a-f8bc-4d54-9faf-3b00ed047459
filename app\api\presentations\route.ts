import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Presentation from '@/models/Presentation';
import { ApiResponse, Presentation as PresentationType } from '@/types';

// GET /api/presentations - Get all presentations for a user
export async function GET(request: NextRequest): Promise<NextResponse<ApiResponse<PresentationType[]>>> {
  try {
    await connectDB();
    
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    
    if (!userId) {
      return NextResponse.json({
        success: false,
        error: 'User ID is required'
      }, { status: 400 });
    }

    const presentations = await Presentation.find({ userId })
      .sort({ 'metadata.createdAt': -1 })
      .lean();

    return NextResponse.json({
      success: true,
      data: presentations.map(p => ({
        ...p,
        _id: p._id.toString()
      }))
    });
  } catch (error) {
    console.error('Error fetching presentations:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch presentations'
    }, { status: 500 });
  }
}

// POST /api/presentations - Create a new presentation
export async function POST(request: NextRequest): Promise<NextResponse<ApiResponse<PresentationType>>> {
  try {
    await connectDB();
    
    const body = await request.json();
    const { userId, title, topic, template = 'modern' } = body;

    if (!userId || !title || !topic) {
      return NextResponse.json({
        success: false,
        error: 'User ID, title, and topic are required'
      }, { status: 400 });
    }

    const presentation = new Presentation({
      userId,
      title,
      topic,
      template,
      slides: [],
      settings: {
        isPublic: false,
        allowComments: false
      },
      metadata: {
        createdAt: new Date(),
        updatedAt: new Date(),
        generationTime: 0,
        wordCount: 0
      }
    });

    const savedPresentation = await presentation.save();

    return NextResponse.json({
      success: true,
      data: {
        ...savedPresentation.toObject(),
        _id: savedPresentation._id.toString()
      }
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating presentation:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to create presentation'
    }, { status: 500 });
  }
}
