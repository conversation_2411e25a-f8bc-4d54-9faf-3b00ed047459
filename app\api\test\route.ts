import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { openRouterClient } from '@/lib/openrouter';

// GET /api/test - Test API connections
export async function GET(request: NextRequest) {
  const tests = {
    mongodb: false,
    openrouter: false,
    timestamp: new Date().toISOString()
  };

  try {
    // Test MongoDB connection
    await connectDB();
    tests.mongodb = true;
  } catch (error) {
    console.error('MongoDB test failed:', error);
  }

  try {
    // Test OpenRouter connection with a simple request
    const response = await openRouterClient.generateContent([
      {
        role: 'user',
        content: 'Say "Hello" in one word only.'
      }
    ]);
    
    if (response && response.length > 0) {
      tests.openrouter = true;
    }
  } catch (error) {
    console.error('OpenRouter test failed:', error);
  }

  return NextResponse.json({
    success: tests.mongodb && tests.openrouter,
    tests,
    message: tests.mongodb && tests.openrouter 
      ? 'All services are working correctly' 
      : 'Some services are not available'
  });
}
