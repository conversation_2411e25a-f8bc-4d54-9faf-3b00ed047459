import { openRouterClient } from './openrouter';
import { GenerationOptions, Slide, SlideType } from '@/types';

interface OutlineResponse {
  title: string;
  slides: {
    title: string;
    type: SlideType;
    content: string;
  }[];
}

interface SlideContentResponse {
  title: string;
  content: string;
  imageKeywords: string;
}

export class AIService {
  async generatePresentationOutline(options: GenerationOptions): Promise<OutlineResponse> {
    try {
      const response = await openRouterClient.generateOutline(
        options.topic,
        options.audience,
        options.slideCount,
        options.tone
      );

      const parsed = JSON.parse(response);
      
      // Validate the response structure
      if (!parsed.title || !Array.isArray(parsed.slides)) {
        throw new Error('Invalid outline response format');
      }

      return parsed;
    } catch (error) {
      console.error('Error generating outline:', error);
      throw new Error('Failed to generate presentation outline');
    }
  }

  async generateSlideContent(
    slideTitle: string, 
    slideDescription: string, 
    presentationTopic: string, 
    tone: string
  ): Promise<SlideContentResponse> {
    try {
      const response = await openRouterClient.generateSlideContent(
        slideTitle,
        slideDescription,
        presentationTopic,
        tone
      );

      const parsed = JSON.parse(response);
      
      // Validate the response structure
      if (!parsed.title || !parsed.content) {
        throw new Error('Invalid slide content response format');
      }

      return parsed;
    } catch (error) {
      console.error('Error generating slide content:', error);
      throw new Error('Failed to generate slide content');
    }
  }

  async generateFullPresentation(options: GenerationOptions): Promise<{
    title: string;
    slides: Slide[];
    generationTime: number;
  }> {
    const startTime = Date.now();

    try {
      // Step 1: Generate outline
      const outline = await this.generatePresentationOutline(options);

      // Step 2: Generate detailed content for each slide
      const slides: Slide[] = [];
      
      for (let i = 0; i < outline.slides.length; i++) {
        const outlineSlide = outline.slides[i];
        
        // Generate detailed content
        const slideContent = await this.generateSlideContent(
          outlineSlide.title,
          outlineSlide.content,
          options.topic,
          options.tone
        );

        const slide: Slide = {
          _id: `slide_${i + 1}`,
          type: outlineSlide.type,
          title: slideContent.title,
          content: slideContent.content,
          order: i + 1,
          layout: this.getDefaultLayout(outlineSlide.type),
          imageAlt: slideContent.imageKeywords
        };

        slides.push(slide);
      }

      const generationTime = Date.now() - startTime;

      return {
        title: outline.title,
        slides,
        generationTime
      };
    } catch (error) {
      console.error('Error generating full presentation:', error);
      throw new Error('Failed to generate presentation');
    }
  }

  private getDefaultLayout(slideType: SlideType): string {
    switch (slideType) {
      case 'title':
        return 'title-only';
      case 'image':
        return 'image-left';
      case 'conclusion':
        return 'content-center';
      default:
        return 'content-center';
    }
  }
}

export const aiService = new AIService();
