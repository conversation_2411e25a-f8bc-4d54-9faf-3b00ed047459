interface OpenRouterMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

interface OpenRouterResponse {
  choices: {
    message: {
      content: string;
    };
  }[];
}

class OpenRouterClient {
  private apiKey: string;
  private baseUrl = 'https://openrouter.ai/api/v1';

  constructor() {
    this.apiKey = process.env.OPENROUTER_API_KEY || '';
    if (!this.apiKey) {
      throw new Error('OPENROUTER_API_KEY is required');
    }
  }

  async generateContent(messages: OpenRouterMessage[], model = 'deepseek/deepseek-chat-v3-0324:free'): Promise<string> {
    try {
      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
          'X-Title': 'Frames AI Presentation Generator',
        },
        body: JSON.stringify({
          model,
          messages,
          temperature: 0.7,
          max_tokens: 4000,
        }),
      });

      if (!response.ok) {
        const error = await response.text();
        throw new Error(`OpenRouter API error: ${response.status} - ${error}`);
      }

      const data: OpenRouterResponse = await response.json();
      return data.choices[0]?.message?.content || '';
    } catch (error) {
      console.error('OpenRouter API error:', error);
      throw new Error('Failed to generate content with AI');
    }
  }

  async generateOutline(topic: string, audience: string, slideCount: number, tone: string): Promise<string> {
    const messages: OpenRouterMessage[] = [
      {
        role: 'system',
        content: `You are an expert presentation designer. Create a structured outline for a presentation.
        
        Rules:
        - Return ONLY a JSON object with this exact structure
        - No additional text or explanations
        - Each slide should have: title, type, content (brief description)
        - Slide types: "title", "content", "image", "conclusion"
        - Make it engaging and well-structured`
      },
      {
        role: 'user',
        content: `Create a ${slideCount}-slide presentation outline about "${topic}" for ${audience} audience with ${tone} tone.
        
        Return JSON format:
        {
          "title": "Presentation Title",
          "slides": [
            {
              "title": "Slide Title",
              "type": "title|content|image|conclusion",
              "content": "Brief description of slide content"
            }
          ]
        }`
      }
    ];

    return this.generateContent(messages);
  }

  async generateSlideContent(slideTitle: string, slideDescription: string, presentationTopic: string, tone: string): Promise<string> {
    const messages: OpenRouterMessage[] = [
      {
        role: 'system',
        content: `You are an expert content writer for presentations. Generate detailed, engaging content for a single slide.
        
        Rules:
        - Return ONLY a JSON object with this exact structure
        - No additional text or explanations
        - Content should be clear, concise, and engaging
        - Use bullet points for main content when appropriate`
      },
      {
        role: 'user',
        content: `Generate content for a slide titled "${slideTitle}" about "${presentationTopic}".
        Description: ${slideDescription}
        Tone: ${tone}
        
        Return JSON format:
        {
          "title": "Final slide title",
          "content": "Detailed slide content with bullet points if needed",
          "imageKeywords": "3-5 keywords for finding relevant images"
        }`
      }
    ];

    return this.generateContent(messages);
  }
}

export const openRouterClient = new OpenRouterClient();
