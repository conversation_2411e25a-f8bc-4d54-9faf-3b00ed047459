import mongoose, { Schema, Document } from 'mongoose';
import { 
  Presentation as PresentationType, 
  Slide, 
  PresentationSettings, 
  PresentationMetadata,
  SlideType,
  SlideLayout
} from '@/types';

export interface PresentationDocument extends Omit<PresentationType, '_id'>, Document {}

const SlideSchema = new Schema<Slide>({
  type: { 
    type: String, 
    enum: ['title', 'content', 'image', 'chart', 'conclusion'], 
    required: true 
  },
  title: { type: String, required: true },
  content: { type: String, required: true },
  imageUrl: { type: String },
  imageAlt: { type: String },
  order: { type: Number, required: true },
  layout: { 
    type: String, 
    enum: ['title-only', 'content-left', 'content-center', 'image-left', 'image-right', 'full-image'],
    default: 'content-center'
  },
});

const PresentationSettingsSchema = new Schema<PresentationSettings>({
  isPublic: { type: Boolean, default: false },
  allowComments: { type: Boolean, default: false },
  shareLink: { type: String },
});

const PresentationMetadataSchema = new Schema<PresentationMetadata>({
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
  generationTime: { type: Number, default: 0 },
  wordCount: { type: Number, default: 0 },
});

const PresentationSchema = new Schema<PresentationDocument>({
  userId: { type: String, required: true, index: true },
  title: { type: String, required: true },
  topic: { type: String, required: true },
  slides: [SlideSchema],
  template: { type: String, default: 'modern' },
  settings: { type: PresentationSettingsSchema, default: () => ({}) },
  metadata: { type: PresentationMetadataSchema, default: () => ({}) },
}, {
  timestamps: true,
});

// Index for efficient queries
PresentationSchema.index({ userId: 1, createdAt: -1 });

export default mongoose.models.Presentation || mongoose.model<PresentationDocument>('Presentation', PresentationSchema);
