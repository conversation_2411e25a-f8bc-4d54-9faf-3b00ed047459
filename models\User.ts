import mongoose, { Schema, Document } from 'mongoose';
import { User as UserType, UserUsage, UserPreferences } from '@/types';

export interface UserDocument extends Omit<UserType, '_id'>, Document {}

const UserUsageSchema = new Schema<UserUsage>({
  presentations: { type: Number, default: 0 },
  imagesGenerated: { type: Number, default: 0 },
  exportsThisMonth: { type: Number, default: 0 },
});

const UserPreferencesSchema = new Schema<UserPreferences>({
  defaultTemplate: { type: String, default: 'modern' },
  autoSave: { type: Boolean, default: true },
});

const UserSchema = new Schema<UserDocument>({
  clerkId: { type: String, required: true, unique: true },
  email: { type: String, required: true, unique: true },
  subscription: { 
    type: String, 
    enum: ['free', 'pro', 'team'], 
    default: 'free' 
  },
  usage: { type: UserUsageSchema, default: () => ({}) },
  preferences: { type: UserPreferencesSchema, default: () => ({}) },
}, {
  timestamps: true,
});

// Prevent re-compilation during development
export default mongoose.models.User || mongoose.model<UserDocument>('User', UserSchema);
