// User Types
export interface User {
  _id: string;
  clerkId: string;
  email: string;
  subscription: 'free' | 'pro' | 'team';
  usage: UserUsage;
  preferences: UserPreferences;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserUsage {
  presentations: number;
  imagesGenerated: number;
  exportsThisMonth: number;
}

export interface UserPreferences {
  defaultTemplate: string;
  autoSave: boolean;
}

// Slide Types
export interface Slide {
  _id: string;
  type: SlideType;
  title: string;
  content: string;
  imageUrl?: string;
  imageAlt?: string;
  order: number;
  layout: SlideLayout;
}

export type SlideType = 'title' | 'content' | 'image' | 'chart' | 'conclusion';
export type SlideLayout = 'title-only' | 'content-left' | 'content-center' | 'image-left' | 'image-right' | 'full-image';

// Presentation Types
export interface Presentation {
  _id: string;
  userId: string;
  title: string;
  topic: string;
  slides: Slide[];
  template: string;
  settings: PresentationSettings;
  metadata: PresentationMetadata;
}

export interface PresentationSettings {
  isPublic: boolean;
  allowComments: boolean;
  shareLink?: string;
}

export interface PresentationMetadata {
  createdAt: Date;
  updatedAt: Date;
  generationTime: number;
  wordCount: number;
}

// Generation Options
export interface GenerationOptions {
  topic: string;
  audience: 'business' | 'academic' | 'general' | 'technical';
  slideCount: number;
  tone: 'professional' | 'casual' | 'academic' | 'creative';
  template: string;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface GenerationProgress {
  step: 'outline' | 'content' | 'images' | 'formatting' | 'complete';
  progress: number;
  message: string;
}
